{"name": "battle-simulator", "version": "1.0.0", "description": "战斗模拟器 - 基于单位配置的作战模拟系统", "main": "battle_simulator.js", "scripts": {"start": "node run_simulation.js", "simulate": "node battle_simulator.js", "test": "node -e \"console.log('测试模拟器...'); const {BattleSimulator} = require('./battle_simulator'); const sim = new BattleSimulator(); console.log('模拟器初始化成功');\""}, "keywords": ["battle", "simulator", "game", "combat", "strategy"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=12.0.0"}}