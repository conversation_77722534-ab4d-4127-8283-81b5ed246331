# 作战模拟器使用说明

## 概述
这是一个基于Node.js的作战模拟器，能够从unit.js中读取单位配置，进行自动化的战斗模拟。

## 文件结构
```
E:\$Claude\T014\
├── unit.js              # 单位配置文件（原有）
├── config.js            # 模拟器配置文件
├── battle_simulator.js  # 核心模拟器代码
├── run_simulation.js    # 启动脚本
├── package.json         # 项目配置
└── README.md           # 本说明文件
```

## 快速开始

### 1. 运行模拟器
```bash
# 方式1：使用npm脚本
npm start

# 方式2：直接运行
node run_simulation.js

# 方式3：直接运行核心文件
node battle_simulator.js
```

### 2. 测试安装
```bash
npm test
```

## 配置说明

### 战斗配置 (config.js)
```javascript
battle: {
  totalBattles: 100,        // 总战斗场次
  unitsPerTeam: 10,         // 每队单位数量
  mapSize: { width: 50, height: 50 },  // 地图大小
  initialDistance: 30,      // 初始队伍间距
  maxRounds: 1000,          // 最大回合数
  timeStep: 0.1             // 时间步长
}
```

### 单位配置
- `randomHpVariation`: 随机血量变化 (±10%)
- `randomAtkVariation`: 随机攻击力变化 (±5%)
- `baseHitRate`: 基础命中率 (90%)
- `defaultCritDamage`: 默认暴击伤害倍数 (2.0)

### AI配置
- `targetStrategy`: 目标选择策略
  - `nearest`: 最近敌人
  - `weakest`: 最弱敌人
  - `strongest`: 最强敌人
  - `random`: 随机选择

## 单位属性说明

### 从unit.js读取的属性：
- `Atk`: 攻击力
- `Hp`: 生命值
- `AtkRange`: 攻击范围
- `AtkSpeed`: 攻击速度
- `Speed`: 移动速度
- `Crit`: 暴击率
- `CritDamage`: 暴击伤害倍数
- `Dodge`: 闪避率
- `IsRangedUnit`: 是否为远程单位
- `BulletSpeed`: 子弹速度（远程单位）

### 特殊技能 (Trait)：
- `UnitRingAtk`: 环形攻击伤害倍数（斧兵）
- `UnitShieldBuff`: 盾牌减伤（盾兵）
- `UnitAoeAtkExplosion`: AOE爆炸攻击（法师）

## 战斗机制

### 1. 移动系统
- 单位会向最近的敌人移动
- 当进入攻击范围后停止移动
- 移动速度基于单位的Speed属性

### 2. 攻击系统
- 攻击冷却时间 = 1 / AtkSpeed
- 命中判定：baseHitRate - target.dodge
- 暴击判定：基于单位的Crit属性
- 伤害计算：Atk * CritDamage (暴击时)

### 3. 特殊技能
- 斧兵：环形攻击，伤害提升50%
- 盾兵：受到伤害减少30%
- 法师：AOE攻击（待实现）

## 统计输出

### 战斗结果统计
- 队伍1胜率
- 队伍2胜率
- 平局率

### 单位表现统计
- 参战次数
- 胜率
- 平均伤害输出
- 平均击杀数
- 命中率
- 暴击率

## 自定义配置

### 修改战斗场次
```javascript
// 在config.js中修改
battle: {
  totalBattles: 200  // 改为200场
}
```

### 修改队伍规模
```javascript
battle: {
  unitsPerTeam: 5  // 改为每队5个单位
}
```

### 启用详细日志
```javascript
logging: {
  verbose: true,  // 显示详细战斗过程
  saveBattleLog: true  // 保存日志到文件
}
```

## 扩展功能

### 添加新单位
在unit.js中添加新的单位配置即可，模拟器会自动识别。

### 添加新技能
在Unit类中的attack方法中添加新的trait处理逻辑。

### 修改AI策略
在config.js中修改ai.targetStrategy和ai.moveStrategy。

## 故障排除

### 1. 模块导入错误
确保所有文件都在同一目录下，且路径正确。

### 2. 单位数据格式错误
检查unit.js中的数据格式是否正确。

### 3. 配置参数错误
检查config.js中的数值范围是否合理。

## 性能优化建议

1. 对于大量战斗场次，可以关闭详细日志
2. 调整timeStep可以平衡精度和性能
3. 减少maxRounds可以避免死循环

## 版本信息
- 版本: 1.0.0
- Node.js要求: >=12.0.0
- 依赖: 无外部依赖

## 更新日志
- v1.0.0: 初始版本，支持基本战斗模拟
