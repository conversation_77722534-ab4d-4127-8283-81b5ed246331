// 作战模拟器配置文件
module.exports = {
  // 战斗配置
  battle: {
    // 战斗场次数
    totalBattles: 1000,
    // 每队选取的单位数量
    unitsPerTeam: 20,
    // 地图大小
    mapSize: {
      width: 50,
      height: 50
    },
    // 初始队伍间距
    initialDistance: 30,
    // 最大战斗回合数（防止无限循环）
    maxRounds: 10000,
    // 时间步长（秒）
    timeStep: 0.1
  },
  
  // 单位配置
  units: {
    // 是否启用随机血量变化 (±10%)
    randomHpVariation: true,
    // 是否启用随机攻击力变化 (±5%)
    randomAtkVariation: true,
    // 基础命中率
    baseHitRate: 1,
    // 暴击伤害倍数（如果单位没有CritDamage字段）
    defaultCritDamage: 2.0,
    // 默认闪避率（如果单位没有Dodge字段）
    defaultDodge: 0.05,
    // 默认暴击率（如果单位没有Crit字段）
    defaultCrit: 0.05
  },
  
  // 战斗AI配置
  ai: {
    // 目标选择策略: 'nearest', 'weakest', 'strongest', 'random'
    targetStrategy: 'nearest',
    // 移动策略: 'aggressive', 'defensive', 'optimal'
    moveStrategy: 'aggressive',
    // 是否启用战术撤退
    enableRetreat: false,
    // 撤退血量阈值（百分比）
    retreatThreshold: 0.2
  },
  
  // 日志配置
  logging: {
    // 是否显示详细战斗日志
    verbose: false,
    // 是否显示每场战斗结果
    showBattleResults: true,
    // 是否显示最终统计
    showFinalStats: true,
    // 是否保存战斗记录到文件
    saveBattleLog: false,
    // 日志文件路径
    logFilePath: './battle_log.txt'
  },
  
  // 统计配置
  statistics: {
    // 是否统计单位表现
    trackUnitPerformance: true,
    // 是否统计伤害输出
    trackDamageOutput: true,
    // 是否统计存活时间
    trackSurvivalTime: true,
    // 是否统计命中率
    trackHitRate: true
  }
};
