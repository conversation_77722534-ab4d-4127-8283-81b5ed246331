// Trait 技能系统模块
class TraitSystem {
  
  // 处理旋转攻击 (UnitRingAtk)
  static handleRingAttack(attacker, allEnemies, damage, range) {
    const affectedTargets = [];
    
    // 找到攻击范围内的所有敌人
    allEnemies.forEach(enemy => {
      if (enemy.alive && attacker.distanceTo(enemy) <= range) {
        affectedTargets.push(enemy);
      }
    });
    
    // 对范围内所有敌人造成伤害
    affectedTargets.forEach(target => {
      target.takeDamage(damage);
      attacker.stats.damageDealt += damage;
      
      if (!target.alive) {
        attacker.stats.kills++;
      }
    });
    
    return affectedTargets;
  }
  
  // 处理护盾减伤 (UnitShieldBuff)
  static applyShieldReduction(damage, shieldPercent) {
    return Math.round(damage * (1 - shieldPercent));
  }
  
  // 处理远程爆炸攻击 (UnitAoeAtkExplosion)
  static handleExplosionAttack(attacker, primaryTarget, allEnemies, damage, range, damageMultiplier) {
    const affectedTargets = [];
    
    // 找到爆炸范围内的所有敌人
    allEnemies.forEach(enemy => {
      if (enemy.alive && primaryTarget.distanceTo(enemy) <= range) {
        affectedTargets.push(enemy);
      }
    });
    
    // 对范围内所有敌人造成爆炸伤害
    const explosionDamage = Math.round(damage * damageMultiplier);
    affectedTargets.forEach(target => {
      // 主目标受到完整伤害，其他目标受到爆炸伤害
      const finalDamage = target === primaryTarget ? damage : explosionDamage;
      target.takeDamage(finalDamage);
      attacker.stats.damageDealt += finalDamage;
      
      if (!target.alive) {
        attacker.stats.kills++;
      }
    });
    
    return affectedTargets;
  }
  
  // 检查单位是否有特定 Trait
  static hasTrait(unit, traitName) {
    return unit.trait && unit.trait[traitName] !== undefined;
  }
  
  // 获取 Trait 参数
  static getTraitParam(unit, traitName) {
    if (!this.hasTrait(unit, traitName)) return null;
    return unit.trait[traitName];
  }
}

module.exports = TraitSystem;